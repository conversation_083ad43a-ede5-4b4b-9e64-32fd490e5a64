// Signal V1 Service Worker
// Provides offline functionality and caching for the trading application

const CACHE_NAME = 'signal-v1-cache-v1';
const STATIC_CACHE_NAME = 'signal-v1-static-v1';
const DYNAMIC_CACHE_NAME = 'signal-v1-dynamic-v1';

// Static assets to cache
const STATIC_ASSETS = [
  '/',
  '/portfolio',
  '/signals',
  '/trading',
  '/analytics',
  '/settings',
  '/manifest.json',
  // Add other static assets as needed
];

// API endpoints that should be cached
const CACHEABLE_APIS = [
  '/api/portfolio',
  '/api/signals',
  '/api/prices',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('Service Worker: Static assets cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static assets', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }

  // Skip external requests (except for APIs we want to cache)
  if (url.origin !== self.location.origin && !isCacheableAPI(url.href)) {
    return;
  }

  // Handle different types of requests
  if (isStaticAsset(url.pathname)) {
    // Static assets - cache first strategy
    event.respondWith(cacheFirst(request));
  } else if (isAPIRequest(url.pathname)) {
    // API requests - network first with cache fallback
    event.respondWith(networkFirst(request));
  } else {
    // Pages - network first with cache fallback
    event.respondWith(networkFirst(request));
  }
});

// Cache first strategy - for static assets
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    console.error('Cache first strategy failed:', error);
    return new Response('Offline - Asset not available', { status: 503 });
  }
}

// Network first strategy - for dynamic content
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network request failed, trying cache:', error);
    
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline page for navigation requests
    if (request.mode === 'navigate') {
      return caches.match('/') || new Response('Offline', { status: 503 });
    }

    return new Response('Offline - Content not available', { status: 503 });
  }
}

// Helper functions
function isStaticAsset(pathname) {
  return pathname.startsWith('/_next/') || 
         pathname.startsWith('/icons/') || 
         pathname.startsWith('/images/') ||
         pathname.endsWith('.js') ||
         pathname.endsWith('.css') ||
         pathname.endsWith('.png') ||
         pathname.endsWith('.jpg') ||
         pathname.endsWith('.svg') ||
         pathname.endsWith('.ico');
}

function isAPIRequest(pathname) {
  return pathname.startsWith('/api/');
}

function isCacheableAPI(url) {
  return CACHEABLE_APIS.some(api => url.includes(api));
}

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'background-sync-trades') {
    event.waitUntil(syncPendingTrades());
  } else if (event.tag === 'background-sync-signals') {
    event.waitUntil(syncPendingSignals());
  }
});

// Sync pending trades when back online
async function syncPendingTrades() {
  try {
    console.log('Service Worker: Syncing pending trades');
    
    // Get pending trades from IndexedDB or localStorage
    const pendingTrades = await getPendingTrades();
    
    for (const trade of pendingTrades) {
      try {
        const response = await fetch('/api/trades', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(trade),
        });
        
        if (response.ok) {
          await removePendingTrade(trade.id);
          console.log('Service Worker: Trade synced successfully', trade.id);
        }
      } catch (error) {
        console.error('Service Worker: Failed to sync trade', trade.id, error);
      }
    }
  } catch (error) {
    console.error('Service Worker: Error syncing pending trades', error);
  }
}

// Sync pending signals when back online
async function syncPendingSignals() {
  try {
    console.log('Service Worker: Syncing pending signals');
    // Implementation would depend on how signals are stored offline
  } catch (error) {
    console.error('Service Worker: Error syncing pending signals', error);
  }
}

// Helper functions for offline storage (simplified)
async function getPendingTrades() {
  // This would typically use IndexedDB
  return JSON.parse(localStorage.getItem('pendingTrades') || '[]');
}

async function removePendingTrade(tradeId) {
  const pendingTrades = await getPendingTrades();
  const updatedTrades = pendingTrades.filter(trade => trade.id !== tradeId);
  localStorage.setItem('pendingTrades', JSON.stringify(updatedTrades));
}

// Push notification handling
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received');
  
  const options = {
    body: 'You have new trading signals!',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: 'explore',
        title: 'View Signals',
        icon: '/icons/checkmark.png',
      },
      {
        action: 'close',
        title: 'Close',
        icon: '/icons/xmark.png',
      },
    ],
  };

  if (event.data) {
    const data = event.data.json();
    options.body = data.body || options.body;
    options.data = { ...options.data, ...data };
  }

  event.waitUntil(
    self.registration.showNotification('Signal V1', options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked');
  
  event.notification.close();

  if (event.action === 'explore') {
    event.waitUntil(
      clients.openWindow('/signals')
    );
  } else if (event.action === 'close') {
    // Just close the notification
  } else {
    // Default action - open the app
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('Service Worker: Loaded');
