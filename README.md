# Signal V1 - Solana Trading Bot

> Comprehensive mobile-first trading application integrating Telegram signal monitoring with automated Solana trading via Jupiter DEX.

[![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC)](https://tailwindcss.com/)
[![Solana](https://img.shields.io/badge/Solana-Web3.js-9945FF)](https://solana.com/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-3ECF8E)](https://supabase.com/)

## 🚀 Features

### Core Functionality
- **📡 Telegram Signal Monitoring**: Real-time parsing and validation of trading signals from Telegram channels
- **🤖 Automated Trading**: Jupiter DEX integration for seamless token swaps with configurable parameters
- **💼 Portfolio Management**: Comprehensive tracking of positions, P&L, and performance analytics
- **📊 Real-time Price Tracking**: Live price updates with historical charts and market data
- **⚙️ Risk Management**: Stop-loss, profit-taking, and position size controls

### Technical Features
- **📱 Progressive Web App**: Full PWA support with offline functionality and push notifications
- **🔒 Security First**: Input validation, rate limiting, and comprehensive error handling
- **🎨 Mobile-First Design**: Responsive UI optimized for mobile trading
- **⚡ Real-time Updates**: Live price feeds and instant signal processing
- **🧪 Comprehensive Testing**: Unit tests with 70%+ coverage and CI/CD ready

## 🛠️ Tech Stack

### Frontend
- **Next.js 15**: React framework with App Router
- **TypeScript**: Type-safe development
- **Tailwind CSS 4**: Utility-first styling
- **Recharts**: Interactive charts and analytics

### Blockchain
- **Solana Web3.js**: Blockchain interaction
- **Wallet Adapters**: Multi-wallet support (Phantom, Solflare, Backpack, Glow)
- **Jupiter API**: DEX aggregation and trading

### Backend & Database
- **Supabase**: Backend-as-a-Service
- **PostgreSQL**: Relational database
- **Real-time subscriptions**: Live data updates

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn
- Supabase account
- Solana wallet (Phantom, Solflare, etc.)

### Installation

1. **Clone and install**
   ```bash
   git clone https://github.com/your-username/signal-v1.git
   cd signal-v1
   npm install
   ```

2. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

   Configure your environment variables:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

   # Solana Configuration
   NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

   # Telegram Bot (Optional)
   TELEGRAM_BOT_TOKEN=your_bot_token
   ```

3. **Start Development**
   ```bash
   npm run dev
   ```

4. **Open Application**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Usage Guide

### Getting Started
1. **Connect Wallet**: Click "Connect Wallet" and select your preferred Solana wallet
2. **Configure Settings**: Go to Settings → Trading to set your trading parameters
3. **Add Telegram Channels**: Configure signal monitoring in Settings → Signals
4. **Start Auto Trading**: Enable automated trading in the Trading page

### Trading Configuration
- **Default Trade Amount**: Amount of SOL to trade per signal (0.01 - 100 SOL)
- **Max Position Size**: Maximum percentage of portfolio per position (1-50%)
- **Stop Loss**: Automatic stop loss percentage (1-90%)
- **Profit Taking**: Automated profit taking at 2x, 3x, 5x levels

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests for CI
npm run test:ci
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
vercel --prod
```

### Docker
```bash
docker build -t signal-v1 .
docker run -p 3000:3000 signal-v1
```

## ⚠️ Disclaimer

This software is for educational purposes only. Trading cryptocurrencies involves substantial risk of loss. Users should never invest more than they can afford to lose and should test thoroughly with small amounts first.

## 📄 License

MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ for the Solana ecosystem**
